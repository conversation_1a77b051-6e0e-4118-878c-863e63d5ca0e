import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enTranslations from './locales/en.json';
import myTranslations from './locales/my.json';

const resources = {
  en: {
    translation: enTranslations
  },
  'en-US': {
    translation: enTranslations
  },
  'en-GB': {
    translation: enTranslations
  },
  my: {
    translation: myTranslations
  },
  'my-MM': {
    translation: myTranslations
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',

    // Handle language variants (en-US -> en, my-MM -> my)
    load: 'languageOnly',

    interpolation: {
      escapeValue: false, // React already does escaping
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'i18nextLng',
    },

    react: {
      useSuspense: false,
    },
  });

export default i18n;
